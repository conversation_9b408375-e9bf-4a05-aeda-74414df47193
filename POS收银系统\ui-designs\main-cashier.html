<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS收银系统 - 主界面</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/cashier-merged.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Vue.js 2 CDN -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
</head>
<body>
    <div id="app">
        <!-- 顶部导航栏 -->
        <header class="header">
            <div class="header-left">
                <div class="logo">
                    <i class="fas fa-cash-register"></i>
                    <span>POS收银系统</span>
                </div>
                <div class="store-info">
                    <span class="store-name">{{ storeInfo.name }}</span>
                    <span class="operator">收银员：{{ storeInfo.operator }}</span>
                </div>
            </div>
            <div class="header-right">
                <div class="time-display">
                    <i class="fas fa-clock"></i>
                    <span>{{ currentTime }}</span>
                </div>
                <div class="user-menu" @click="toggleUserMenu">
                    <i class="fas fa-user-circle"></i>
                    <span>{{ storeInfo.operator }}</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 左侧商品区域 -->
            <section class="left-panel">
                <!-- 搜索和分类 -->
                <div class="search-section">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text"
                               placeholder="输入商品名称或扫码..."
                               v-model="searchKeyword"
                               @input="onSearchInput">
                        <button class="scan-btn" @click="showBarcodeScanner">
                            <i class="fas fa-barcode"></i>
                        </button>
                    </div>
                    <div class="category-tabs">
                        <button v-for="category in categories"
                                :key="category"
                                class="category-tab"
                                :class="{ active: selectedCategory === category }"
                                @click="selectCategory(category)">
                            {{ category }}
                        </button>
                    </div>
                </div>

                <!-- 商品网格 -->
                <div class="products-grid" id="products-container" @scroll="handleScroll" tabindex="0">
                    <div v-if="filteredProducts.length === 0" class="no-results-message">
                        <i class="fas fa-box-open"></i>
                        <p>暂无商品</p>
                    </div>
                    <div v-for="product in infiniteScrollProducts"
                         :key="product.id"
                         class="product-card"
                         :class="{ 'out-of-stock': product.stock === 0 }"
                         @click="showProductDetail(product)">
                        <div class="product-image">
                            <img :src="product.image" :alt="product.name" loading="lazy">
                            <div class="image-overlay"></div>
                            <span v-if="product.tags.includes('热销')" class="product-tag tag-hot">热销</span>
                            <span v-if="product.tags.includes('新品')" class="product-tag tag-new">新品</span>
                            <span v-if="product.tags.includes('折扣')" class="product-tag tag-sale">特惠</span>
                        </div>
                        <div class="product-info">
                            <h4>{{ product.name }}</h4>
                            <p class="price">
                                ¥{{ product.price.toFixed(2) }}
                                <span v-if="product.originalPrice" class="original-price">
                                    ¥{{ product.originalPrice.toFixed(2) }}
                                </span>
                            </p>
                            <p class="stock" :class="getStockClass(product.stock)">
                                {{ getStockText(product) }}
                            </p>
                        </div>
                        <button class="add-btn"
                                :disabled="product.stock === 0"
                                :class="{ 'has-quantity': getProductQuantityInCart(product.id) > 0 }"
                                @click.stop="addToCart(product)">
                            <span v-if="getProductQuantityInCart(product.id) > 0" class="quantity-badge">
                                {{ getProductQuantityInCart(product.id) }}
                            </span>
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>

                    <!-- 加载更多指示器 -->
                    <div v-if="isLoading" class="loading-indicator">
                        <div class="loading-spinner"></div>
                        <span>正在加载更多商品...</span>
                    </div>

                    <!-- 加载错误提示 -->
                    <div v-if="loadError" class="load-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>{{ loadErrorMessage }}</span>
                        <button class="retry-btn" @click="retryLoad">
                            <i class="fas fa-redo"></i>
                            重试
                        </button>
                    </div>

                    <!-- 没有更多数据提示 -->
                    <div v-if="!hasMoreData && displayedProducts.length > 0 && !loadError" class="no-more-data">
                        <i class="fas fa-check-circle"></i>
                        <span>已显示全部商品</span>
                    </div>


                </div>

                <!-- 分页控件 -->
                <div v-if="totalPages > 1" class="pagination-container">
                    <div class="pagination">
                        <div class="page-item prev"
                             :class="{ disabled: currentPage === 1 }"
                             @click="changePage(currentPage - 1)">
                            <i class="fas fa-chevron-left"></i>
                        </div>
                        <div class="page-numbers">
                            <div v-for="page in visiblePages"
                                 :key="page"
                                 class="page-item"
                                 :class="{ active: page === currentPage, ellipsis: page === '...' }"
                                 @click="page !== '...' && changePage(page)">
                                {{ page }}
                            </div>
                        </div>
                        <div class="page-item next"
                             :class="{ disabled: currentPage === totalPages }"
                             @click="changePage(currentPage + 1)">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                        <div class="page-info">
                            {{ currentPage }}/{{ totalPages }} 页，共 {{ filteredProducts.length }} 件商品
                        </div>
                    </div>
                </div>
            </section>

            <!-- 右侧收银区域 -->
            <section class="right-panel">
                <!-- 会员信息 -->
                <div class="member-section">
                    <div class="member-info">
                        <i class="fas fa-user"></i>
                        <span>{{ currentMember ? currentMember.name : '未绑定会员' }}</span>
                    </div>
                    <button class="bind-member-btn" @click="showMemberBindModal">
                        <i class="fas fa-qrcode"></i>
                        {{ currentMember ? '更换会员' : '绑定会员' }}
                    </button>
                </div>

                <!-- 订单商品列表 -->
                <div class="order-section">
                    <!-- 当前订单标题 -->
                    <div class="current-order-title">
                        <div class="order-title">
                            <i class="fas fa-shopping-cart cart-icon"></i>
                            当前订单
                            <span class="order-count" v-if="cartItems.length > 0">{{ cartItems.length }}</span>
                        </div>
                        <button class="clear-btn" @click="clearCart" v-if="cartItems.length > 0">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>

                    <!-- 购物车内容 -->
                    <template v-if="cartItems.length === 0">
                        <div class="empty-cart-container">
                            <div class="empty-cart">
                                <div class="empty-cart-icon">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <p class="empty-cart-text">购物车为空</p>
                                <p class="empty-cart-hint">点击商品上的 + 按钮添加商品</p>
                            </div>
                        </div>
                    </template>

                    <template v-else>
                        <div class="cart-items-list" id="cart-items-container">
                            <div v-for="item in cartItems" :key="item.id" class="cart-item">
                                <div class="item-name-price">
                                    <span class="item-name">{{ item.name }}</span>
                                    <span class="item-price">¥{{ item.price.toFixed(2) }}</span>
                                </div>
                                <div class="item-quantity-control">
                                    <div class="quantity-group">
                                        <button class="quantity-btn decrease" 
                                                :disabled="item.quantity <= 1"
                                                @click="updateCartItemQuantity(item.id, -1)">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <input type="text" class="item-quantity" 
                                               :value="item.quantity"
                                               @input="handleQuantityInput(item.id, $event.target.value)"
                                               @blur="validateQuantityInput($event, item.id)">
                                        <button class="quantity-btn increase" 
                                                :disabled="item.quantity >= getProductStock(item.id)"
                                                @click="updateCartItemQuantity(item.id, 1)">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>

                <!-- 订单备注 -->
                <div class="order-notes-section">
                    <div class="notes-header">
                        <h3>订单备注</h3>
                        <button class="notes-expand-btn"
                                title="展开/收起"
                                @click="toggleNotesExpanded">
                            <i class="fas" :class="notesExpanded ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                        </button>
                    </div>
                    <div class="notes-container" v-show="notesExpanded">
                        <div class="notes-input-area">
                            <textarea v-model="orderNotes"
                                      placeholder="请输入订单备注信息（可选）"
                                      rows="2"
                                      maxlength="200"
                                      @input="updateNotesCount"></textarea>
                            <div class="notes-actions">
                                <div class="notes-counter">
                                    <span>{{ orderNotes.length }}</span>/200
                                </div>
                                <div class="notes-quick-buttons">
                                    <button v-for="note in quickNotes"
                                            :key="note"
                                            class="quick-note-btn"
                                            @click="addQuickNote(note)">
                                        {{ note }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 优惠活动 -->
                <div class="discount-section">
                    <div class="discount-header">
                        <h3>优惠活动</h3>
                        <div class="discount-controls">
                            <button class="discount-select-all-btn"
                                    title="全选/全不选"
                                    @click="toggleAllDiscounts">
                                <i class="fas fa-check-double"></i>
                            </button>
                            <button class="discount-expand-btn"
                                    title="展开/收起"
                                    @click="toggleDiscountExpanded">
                                <i class="fas" :class="discountExpanded ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                            </button>
                        </div>
                    </div>
                    <div class="discount-container" v-show="discountExpanded">
                        <div class="discount-items">
                            <div v-for="discount in discountData"
                                 :key="discount.id"
                                 class="discount-item"
                                 :class="{ active: discount.active }">
                                <div class="discount-checkbox">
                                    <input type="checkbox"
                                           :id="'discount-' + discount.id"
                                           v-model="discount.active"
                                           @change="updateCartTotal">
                                    <label :for="'discount-' + discount.id"></label>
                                </div>
                                <div class="discount-info">
                                    <h4>{{ discount.name }}</h4>
                                    <p>{{ discount.description }}</p>
                                </div>
                                <div class="discount-value">{{ discount.displayValue }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 支付区域 -->
                <div class="payment-section">
                    <div class="total-info">
                        <div class="subtotal">
                            <span>小计：</span>
                            <span class="amount">¥{{ cartSubtotal.toFixed(2) }}</span>
                        </div>
                        <div class="discount">
                            <span>优惠：</span>
                            <span class="amount">-¥{{ cartDiscount.toFixed(2) }}</span>
                        </div>
                        <div class="total">
                            <span>总计：</span>
                            <span class="amount">¥{{ cartTotal.toFixed(2) }}</span>
                        </div>
                    </div>

                    <div class="payment-methods">
                        <button class="payment-btn wechat"
                                @click="processPayment('微信支付', $event)">
                            <i class="fab fa-weixin"></i>
                            微信支付
                        </button>
                        <button class="payment-btn alipay"
                                @click="processPayment('支付宝', $event)">
                            <i class="fab fa-alipay"></i>
                            支付宝
                        </button>
                        <button class="payment-btn cash"
                                @click="processPayment('现金支付', $event)">
                            <i class="fas fa-money-bill-wave"></i>
                            现金支付
                        </button>
                        <button class="payment-btn card"
                                @click="processPayment('银行卡', $event)">
                            <i class="fas fa-credit-card"></i>
                            银行卡
                        </button>
                    </div>
                </div>
            </section>
        </main>

        <!-- 底部状态栏 -->
        <footer class="footer">
            <div class="status-info">
                <span class="status-item">
                    <i class="fas fa-wifi"></i>
                    {{ systemStatus.network }}
                </span>
                <span class="status-item">
                    <i class="fas fa-print"></i>
                    {{ systemStatus.printer }}
                </span>
                <span class="status-item">
                    <i class="fas fa-database"></i>
                    {{ systemStatus.database }}
                </span>

            </div>
            <div class="version-info">
                <span>版本 {{ systemStatus.version }}</span>
            </div>
        </footer>

        <!-- 通知容器 -->
        <div v-if="notifications.length > 0" class="notifications-container">
            <div v-for="notification in notifications"
                 :key="notification.id"
                 class="notification"
                 :class="notification.type">
                <div class="notification-icon">
                    <i class="fas" :class="getNotificationIcon(notification.type)"></i>
                </div>
                <div class="notification-content">{{ notification.message }}</div>
                <button class="notification-close" @click="removeNotification(notification.id)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- 加载遮罩 -->
        <div v-if="loading.show" class="loading-overlay">
            <div class="loading-spinner">
                <div class="spinner"></div>
                <div class="loading-text">{{ loading.message }}</div>
            </div>
        </div>

        <!-- 用户下拉菜单 -->
        <div v-if="showUserDropdown" class="user-dropdown active" :style="userDropdownStyle">
            <ul>
                <li @click="handleUserMenuAction('profile')">
                    <i class="fas fa-user"></i> 个人信息
                </li>
                <li @click="handleUserMenuAction('settings')">
                    <i class="fas fa-cog"></i> 系统设置
                </li>
                <li @click="handleUserMenuAction('logout')">
                    <i class="fas fa-sign-out-alt"></i> 退出登录
                </li>
            </ul>
        </div>

        <!-- 会员绑定模态框 -->
        <div v-if="showMemberModal" class="modal-overlay member-bind-modal" @click="closeMemberModal">
            <div class="modal-container member-bind-container" @click.stop>
                <div class="modal-header">
                    <h3><i class="fas fa-user-plus"></i> 绑定会员</h3>
                    <button class="modal-close" @click="closeMemberModal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <!-- 搜索方式选择 -->
                    <div v-if="!foundMember && !showMemberRegistration" class="bind-methods">
                        <div class="bind-method-tabs">
                            <button class="bind-tab"
                                    :class="{ active: memberSearchMethod === 'phone' }"
                                    @click="switchMemberSearchMethod('phone')">
                                <i class="fas fa-mobile-alt"></i> 手机号
                            </button>
                            <button class="bind-tab"
                                    :class="{ active: memberSearchMethod === 'card' }"
                                    @click="switchMemberSearchMethod('card')">
                                <i class="fas fa-id-card"></i> 会员卡
                            </button>
                            <button class="bind-tab"
                                    :class="{ active: memberSearchMethod === 'qr' }"
                                    @click="switchMemberSearchMethod('qr')">
                                <i class="fas fa-qrcode"></i> 扫码
                            </button>
                        </div>



                        <!-- 手机号搜索 -->
                        <div v-if="memberSearchMethod === 'phone'" class="bind-content">
                            <div class="input-group">
                                <label><i class="fas fa-mobile-alt"></i> 手机号码</label>
                                <input type="tel"
                                       v-model="memberSearchValue"
                                       placeholder="请输入手机号码"
                                       maxlength="11"
                                       @keypress.enter="searchMember">
                            </div>
                            <button class="btn btn-primary search-member-btn"
                                    :disabled="memberSearchLoading || !isValidPhone"
                                    @click="searchMember">
                                <i class="fas fa-search" v-if="!memberSearchLoading"></i>
                                <i class="fas fa-spinner fa-spin" v-if="memberSearchLoading"></i>
                                {{ memberSearchLoading ? '查找中...' : '查找会员' }}
                            </button>
                        </div>

                        <!-- 会员卡搜索 -->
                        <div v-if="memberSearchMethod === 'card'" class="bind-content">
                            <div class="input-group">
                                <label><i class="fas fa-id-card"></i> 会员卡号</label>
                                <input type="text"
                                       v-model="memberSearchValue"
                                       placeholder="请输入会员卡号"
                                       @keypress.enter="searchMember">
                            </div>
                            <button class="btn btn-primary search-member-btn"
                                    :disabled="memberSearchLoading || !memberSearchValue.trim()"
                                    @click="searchMember">
                                <i class="fas fa-search" v-if="!memberSearchLoading"></i>
                                <i class="fas fa-spinner fa-spin" v-if="memberSearchLoading"></i>
                                {{ memberSearchLoading ? '查找中...' : '查找会员' }}
                            </button>
                        </div>

                        <!-- 扫码搜索 -->
                        <div v-if="memberSearchMethod === 'qr'" class="bind-content">
                            <div class="qr-scanner">
                                <div class="qr-placeholder">
                                    <i class="fas fa-qrcode"></i>
                                    <p>请使用会员二维码扫描</p>
                                    <button class="btn btn-outline start-scan-btn" @click="startQRScan">
                                        <i class="fas fa-camera"></i> 启动扫描
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 找到会员信息显示 -->
                    <div v-if="foundMember && !showMemberRegistration" class="member-info-display">
                        <div class="member-card">
                            <div class="member-avatar">
                                <img v-if="foundMember.avatar" :src="foundMember.avatar" :alt="foundMember.name">
                                <i v-else class="fas fa-user-circle"></i>
                            </div>
                            <div class="member-details">
                                <h4 class="member-name">{{ foundMember.name }}</h4>
                                <p class="member-level">
                                    <i class="fas fa-crown" v-if="foundMember.level.includes('钻石')"></i>
                                    <i class="fas fa-medal" v-else-if="foundMember.level.includes('金卡')"></i>
                                    <i class="fas fa-award" v-else-if="foundMember.level.includes('银卡')"></i>
                                    <i class="fas fa-user" v-else></i>
                                    {{ foundMember.level }}
                                </p>
                                <div class="member-stats">
                                    <div class="stat-item">
                                        <span class="stat-label">积分</span>
                                        <span class="stat-value">{{ foundMember.points }}</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">折扣</span>
                                        <span class="stat-value">{{ (foundMember.discount * 10).toFixed(1) }}折</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">手机</span>
                                        <span class="stat-value">{{ foundMember.phone }}</span>
                                    </div>
                                </div>
                                <div class="member-actions">
                                    <button class="btn btn-sm btn-outline-light" @click="showPointsManagement = true">
                                        <i class="fas fa-coins"></i> 积分管理
                                    </button>
                                    <button class="btn btn-sm btn-outline-light" @click="viewMemberHistory">
                                        <i class="fas fa-history"></i> 消费记录
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 未找到会员 - 注册新会员 -->
                    <div v-if="showMemberRegistration" class="member-registration">
                        <div class="registration-header">
                            <h4><i class="fas fa-user-plus"></i> 注册新会员</h4>
                            <p>未找到会员信息，请填写以下信息注册新会员</p>
                        </div>
                        <div class="registration-form">
                            <div class="input-group">
                                <label><i class="fas fa-user"></i> 姓名</label>
                                <input type="text"
                                       v-model="newMemberData.name"
                                       placeholder="请输入会员姓名"
                                       maxlength="20">
                            </div>
                            <div class="input-group">
                                <label><i class="fas fa-mobile-alt"></i> 手机号</label>
                                <input type="tel"
                                       v-model="newMemberData.phone"
                                       placeholder="请输入手机号码"
                                       maxlength="11"
                                       :value="memberSearchMethod === 'phone' ? memberSearchValue : ''">
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label><i class="fas fa-birthday-cake"></i> 生日</label>
                                    <input type="date"
                                           v-model="newMemberData.birthday"
                                           placeholder="请选择生日">
                                </div>
                                <div class="form-group">
                                    <label><i class="fas fa-crown"></i> 会员等级</label>
                                    <select v-model="newMemberData.level">
                                        <option value="普通会员">普通会员</option>
                                        <option value="银卡会员">银卡会员</option>
                                        <option value="金卡会员">金卡会员</option>
                                        <option value="钻石会员">钻石会员</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="registration-actions">
                            <button class="btn btn-primary"
                                    :disabled="!isValidNewMember"
                                    @click="registerNewMember">
                                <i class="fas fa-user-plus"></i> 注册并绑定
                            </button>
                            <button class="btn btn-outline" @click="showMemberRegistration = false">
                                <i class="fas fa-arrow-left"></i> 返回搜索
                            </button>
                        </div>
                    </div>

                    <!-- 未找到会员提示 -->
                    <div v-if="!foundMember && !showMemberRegistration && memberSearchValue && !memberSearchLoading"
                         class="member-not-found">
                        <div class="not-found-icon">
                            <i class="fas fa-user-slash"></i>
                        </div>
                        <h4>未找到会员</h4>
                        <p>搜索条件：{{ memberSearchValue }}</p>
                        <div class="member-actions">
                            <button class="btn btn-outline" @click="showMemberRegistration = true">
                                <i class="fas fa-user-plus"></i> 注册新会员
                            </button>
                            <button class="btn btn-outline" @click="continueWithoutMember">
                                <i class="fas fa-shopping-cart"></i> 继续购物
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" @click="closeMemberModal">取消</button>
                    <button v-if="foundMember"
                            class="btn btn-primary"
                            @click="bindMember">
                        <i class="fas fa-link"></i> 绑定会员
                    </button>
                    <button v-if="showMemberRegistration"
                            class="btn btn-primary"
                            :disabled="!isValidNewMember"
                            @click="registerNewMember">
                        <i class="fas fa-user-plus"></i> 注册并绑定
                    </button>
                </div>
            </div>
        </div>

        <!-- 积分管理模态框 -->
        <div v-if="showPointsManagement" class="modal-overlay points-management-modal" @click="closePointsModal">
            <div class="modal-container points-container" @click.stop>
                <div class="modal-header">
                    <h3><i class="fas fa-coins"></i> 积分管理</h3>
                    <button class="modal-close" @click="closePointsModal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="points-info">
                        <div class="current-points">
                            <h4>当前积分</h4>
                            <div class="points-display">{{ foundMember.points }}</div>
                        </div>
                    </div>

                    <div class="points-operations">
                        <div class="operation-tabs">
                            <button class="operation-tab"
                                    :class="{ active: pointsOperation === 'add' }"
                                    @click="pointsOperation = 'add'">
                                <i class="fas fa-plus"></i> 增加积分
                            </button>
                            <button class="operation-tab"
                                    :class="{ active: pointsOperation === 'deduct' }"
                                    @click="pointsOperation = 'deduct'">
                                <i class="fas fa-minus"></i> 扣除积分
                            </button>
                            <button class="operation-tab"
                                    :class="{ active: pointsOperation === 'exchange' }"
                                    @click="pointsOperation = 'exchange'">
                                <i class="fas fa-gift"></i> 积分兑换
                            </button>
                        </div>

                        <div class="operation-content">
                            <!-- 增加积分 -->
                            <div v-if="pointsOperation === 'add'" class="points-form">
                                <div class="input-group">
                                    <label><i class="fas fa-plus-circle"></i> 增加积分</label>
                                    <input type="number"
                                           v-model="pointsAmount"
                                           placeholder="请输入积分数量"
                                           min="1" max="10000">
                                </div>
                                <div class="input-group">
                                    <label><i class="fas fa-comment"></i> 备注说明</label>
                                    <input type="text"
                                           v-model="pointsRemark"
                                           placeholder="请输入备注说明（可选）">
                                </div>
                            </div>

                            <!-- 扣除积分 -->
                            <div v-if="pointsOperation === 'deduct'" class="points-form">
                                <div class="input-group">
                                    <label><i class="fas fa-minus-circle"></i> 扣除积分</label>
                                    <input type="number"
                                           v-model="pointsAmount"
                                           placeholder="请输入积分数量"
                                           :min="1" :max="foundMember.points">
                                </div>
                                <div class="input-group">
                                    <label><i class="fas fa-comment"></i> 扣除原因</label>
                                    <select v-model="pointsRemark">
                                        <option value="">请选择扣除原因</option>
                                        <option value="积分兑换">积分兑换</option>
                                        <option value="违规扣除">违规扣除</option>
                                        <option value="系统调整">系统调整</option>
                                        <option value="其他">其他</option>
                                    </select>
                                </div>
                            </div>

                            <!-- 积分兑换 -->
                            <div v-if="pointsOperation === 'exchange'" class="exchange-list">
                                <div class="exchange-item"
                                     v-for="item in exchangeItems"
                                     :key="item.id"
                                     @click="selectExchangeItem(item)">
                                    <div class="exchange-icon">
                                        <i :class="item.icon"></i>
                                    </div>
                                    <div class="exchange-details">
                                        <h5>{{ item.name }}</h5>
                                        <p>需要 {{ item.points }} 积分</p>
                                    </div>
                                    <div class="exchange-status">
                                        <span v-if="foundMember.points >= item.points" class="available">可兑换</span>
                                        <span v-else class="unavailable">积分不足</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" @click="closePointsModal">取消</button>
                    <button v-if="pointsOperation !== 'exchange'"
                            class="btn btn-primary"
                            :disabled="!isValidPointsOperation"
                            @click="executePointsOperation">
                        <i class="fas fa-check"></i> 确认操作
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Vue.js 应用实例
        new Vue({
            el: '#app',
            data: {
                // 系统信息
                storeInfo: {
                    name: '示例门店',
                    operator: '张三'
                },
                currentTime: '',
                systemStatus: {
                    network: '网络正常',
                    printer: '打印机就绪',
                    database: '数据同步正常',
                    version: 'v1.0.0'
                },

                // UI状态
                showUserDropdown: false,
                userDropdownStyle: {},
                notifications: [],
                loading: {
                    show: false,
                    message: '加载中...'
                },

                // 商品相关
                searchKeyword: '',
                selectedCategory: '全部',
                categories: ['全部', '热销', '饮料', '小吃', '主食', '甜品'],
                currentPage: 1,
                itemsPerPage: 8,
                // 无限滚动相关
                displayedProducts: [], // 当前显示的商品列表
                isLoading: false, // 是否正在加载
                hasMoreData: true, // 是否还有更多数据
                loadThreshold: 50, // 距离底部多少像素时开始加载（改为50px）
                loadError: false, // 加载错误状态
                loadErrorMessage: '', // 错误信息
                scrollDebounceTimer: null, // 滚动防抖定时器
                itemsPerLoad: 8, // 每次加载的商品数量
                retryCount: 0, // 重试次数
                maxRetries: 3, // 最大重试次数

                // 购物车
                cartItems: [],
                currentMember: null,

                // 会员绑定相关
                showMemberModal: false,
                memberSearchMethod: 'phone', // phone, card, qr
                memberSearchValue: '',
                foundMember: null,
                memberSearchLoading: false,
                showMemberRegistration: false,
                newMemberData: {
                    name: '',
                    phone: '',
                    birthday: '',
                    level: '普通会员'
                },

                // 积分管理
                showPointsManagement: false,
                pointsOperation: 'add', // add, deduct, exchange
                pointsAmount: '',
                pointsRemark: '',
                exchangeItems: [
                    { id: 1, name: '5元代金券', points: 500, icon: 'fas fa-ticket-alt' },
                    { id: 2, name: '10元代金券', points: 1000, icon: 'fas fa-ticket-alt' },
                    { id: 3, name: '免费饮料券', points: 300, icon: 'fas fa-coffee' },
                    { id: 4, name: '生日蛋糕券', points: 2000, icon: 'fas fa-birthday-cake' },
                    { id: 5, name: '会员升级券', points: 5000, icon: 'fas fa-crown' }
                ],

                // 订单备注
                orderNotes: '',
                notesExpanded: true,
                quickNotes: ['不要辣', '少糖', '打包', '加急'],

                // 优惠活动
                discountExpanded: true,

                // 商品数据
                productData: [
                    { id: 1, name: '可口可乐', price: 3.50, originalPrice: null, stock: 50, image: 'https://images.unsplash.com/photo-1554866585-cd94860890b7?w=200&h=200&fit=crop&crop=center', category: '饮料', tags: ['热销'] },
                    { id: 2, name: '牛肉汉堡', price: 15.00, originalPrice: 18.00, stock: 20, image: 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=200&h=200&fit=crop&crop=center', category: '主食', tags: ['折扣'] },
                    { id: 3, name: '薯条', price: 8.00, originalPrice: null, stock: 30, image: 'https://images.unsplash.com/photo-1573080496219-bb080dd4f877?w=200&h=200&fit=crop&crop=center', category: '小吃', tags: [] },
                    { id: 4, name: '珍珠奶茶', price: 12.00, originalPrice: null, stock: 25, image: 'https://images.unsplash.com/photo-1525385133512-2f3bdd039054?w=200&h=200&fit=crop&crop=center', category: '饮料', tags: [] },
                    { id: 5, name: '炸鸡', price: 10.00, originalPrice: 12.00, stock: 15, image: 'https://images.unsplash.com/photo-1562967914-608f82629710?w=200&h=200&fit=crop&crop=center', category: '小吃', tags: ['折扣'] },
                    { id: 6, name: '意大利面', price: 18.00, originalPrice: null, stock: 12, image: 'https://images.unsplash.com/photo-1551892374-ecf8754cf8b0?w=200&h=200&fit=crop&crop=center', category: '主食', tags: ['新品'] },
                    { id: 7, name: '矿泉水', price: 2.00, originalPrice: null, stock: 100, image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=200&h=200&fit=crop&crop=center', category: '饮料', tags: [] },
                    { id: 8, name: '沙拉', price: 15.00, originalPrice: null, stock: 8, image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=200&h=200&fit=crop&crop=center', category: '主食', tags: ['低库存'] },
                    { id: 9, name: '咖啡', price: 16.00, originalPrice: 20.00, stock: 40, image: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=200&h=200&fit=crop&crop=center', category: '饮料', tags: ['折扣', '热销'] },
                    { id: 10, name: '三明治', price: 12.00, originalPrice: null, stock: 18, image: 'https://images.unsplash.com/photo-1539252554453-80ab65ce3586?w=200&h=200&fit=crop&crop=center', category: '主食', tags: [] },
                    { id: 11, name: '冰淇淋', price: 8.00, originalPrice: 10.00, stock: 22, image: 'https://images.unsplash.com/photo-1563805042-7684c019e1cb?w=200&h=200&fit=crop&crop=center', category: '甜品', tags: ['折扣'] },
                    { id: 12, name: '蛋糕', price: 20.00, originalPrice: null, stock: 10, image: 'https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=200&h=200&fit=crop&crop=center', category: '甜品', tags: ['新品'] },
                    { id: 13, name: '果汁', price: 10.00, originalPrice: null, stock: 35, image: 'https://images.unsplash.com/photo-1600271886742-f049cd451bba?w=200&h=200&fit=crop&crop=center', category: '饮料', tags: [] },
                    { id: 14, name: '披萨', price: 25.00, originalPrice: 30.00, stock: 15, image: 'https://images.unsplash.com/photo-1513104890138-7c749659a591?w=200&h=200&fit=crop&crop=center', category: '主食', tags: ['折扣', '热销'] },
                    { id: 15, name: '巧克力', price: 5.00, originalPrice: null, stock: 60, image: 'https://images.unsplash.com/photo-1511381939415-e44015466834?w=200&h=200&fit=crop&crop=center', category: '甜品', tags: [] },
                    { id: 16, name: '甜甜圈', price: 6.00, originalPrice: 8.00, stock: 0, image: 'https://images.unsplash.com/photo-1551024506-0bccd828d307?w=200&h=200&fit=crop&crop=center', category: '甜品', tags: ['折扣', '售罄'] }
                ],

                // 优惠活动数据
                discountData: [
                    { id: 1, name: '满30减5', description: '订单满30元减5元', type: 'fullReduction', condition: 30, value: 5, displayValue: '-¥5', active: false },
                    { id: 2, name: '会员9折', description: '会员专享9折优惠', type: 'memberDiscount', value: 0.9, displayValue: '9折', active: false },
                    { id: 3, name: '新用户8折', description: '新用户首单8折', type: 'newUserDiscount', value: 0.8, displayValue: '8折', active: false }
                ],

                // 会员数据库
                memberDatabase: [
                    { id: 1, name: '张三', phone: '13800138001', cardNumber: 'VIP001', level: '金卡会员', points: 1580, discount: 0.9, avatar: null, birthday: '1990-01-15', registerDate: '2023-01-15' },
                    { id: 2, name: '李四', phone: '13800138002', cardNumber: 'VIP002', level: '银卡会员', points: 890, discount: 0.95, avatar: null, birthday: '1985-05-20', registerDate: '2023-03-10' },
                    { id: 3, name: '王五', phone: '13800138003', cardNumber: 'VIP003', level: '普通会员', points: 320, discount: 1.0, avatar: null, birthday: '1992-08-08', registerDate: '2023-06-01' },
                    { id: 4, name: '赵六', phone: '13800138004', cardNumber: 'VIP004', level: '钻石会员', points: 3200, discount: 0.85, avatar: null, birthday: '1988-12-25', registerDate: '2022-12-01' },
                    { id: 5, name: '孙七', phone: '13800138005', cardNumber: 'VIP005', level: '金卡会员', points: 2100, discount: 0.9, avatar: null, birthday: '1995-03-18', registerDate: '2023-02-14' }
                ]
            },

            computed: {
                // 筛选后的商品
                filteredProducts() {
                    let products = this.productData;

                    // 按分类筛选
                    if (this.selectedCategory !== '全部') {
                        if (this.selectedCategory === '热销') {
                            products = products.filter(p => p.tags.includes('热销'));
                        } else {
                            products = products.filter(p => p.category === this.selectedCategory);
                        }
                    }

                    // 按搜索关键词筛选
                    if (this.searchKeyword.trim()) {
                        const keyword = this.searchKeyword.toLowerCase();
                        products = products.filter(p =>
                            p.name.toLowerCase().includes(keyword) ||
                            p.category.toLowerCase().includes(keyword) ||
                            p.tags.some(tag => tag.toLowerCase().includes(keyword))
                        );
                    }

                    return products;
                },

                // 总页数
                totalPages() {
                    return Math.ceil(this.filteredProducts.length / this.itemsPerPage);
                },

                // 当前页商品（保留原有分页逻辑作为备用）
                paginatedProducts() {
                    const start = (this.currentPage - 1) * this.itemsPerPage;
                    const end = start + this.itemsPerPage;
                    return this.filteredProducts.slice(start, end);
                },

                // 无限滚动显示的商品（优先使用）
                infiniteScrollProducts() {
                    return this.displayedProducts.length > 0 ? this.displayedProducts : this.paginatedProducts;
                },

                // 可见页码
                visiblePages() {
                    const pages = [];
                    const total = this.totalPages;
                    const current = this.currentPage;

                    if (total <= 5) {
                        for (let i = 1; i <= total; i++) {
                            pages.push(i);
                        }
                    } else {
                        if (current <= 3) {
                            pages.push(1, 2, 3, '...', total);
                        } else if (current >= total - 2) {
                            pages.push(1, '...', total - 2, total - 1, total);
                        } else {
                            pages.push(1, '...', current - 1, current, current + 1, '...', total);
                        }
                    }

                    return pages;
                },

                // 购物车总数量
                cartTotalQuantity() {
                    return this.cartItems.reduce((sum, item) => sum + item.quantity, 0);
                },

                // 购物车小计
                cartSubtotal() {
                    return this.cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
                },

                // 购物车优惠金额
                cartDiscount() {
                    let discount = 0;
                    const subtotal = this.cartSubtotal;

                    // 应用激活的优惠活动
                    this.discountData.forEach(d => {
                        if (d.active) {
                            switch (d.type) {
                                case 'fullReduction':
                                    if (subtotal >= d.condition) {
                                        discount += d.value;
                                    }
                                    break;
                                case 'memberDiscount':
                                case 'newUserDiscount':
                                    if (d.value < 1) {
                                        discount += subtotal * (1 - d.value);
                                    }
                                    break;
                            }
                        }
                    });

                    // 会员折扣
                    if (this.currentMember && this.currentMember.discount < 1) {
                        const hasOtherDiscount = this.discountData.some(d =>
                            d.active && (d.type.includes('Discount') || d.type.includes('Special'))
                        );
                        if (!hasOtherDiscount) {
                            discount += subtotal * (1 - this.currentMember.discount);
                        }
                    }

                    return discount;
                },

                // 购物车总计
                cartTotal() {
                    return Math.max(0, this.cartSubtotal - this.cartDiscount);
                },

                // 验证手机号
                isValidPhone() {
                    return /^1[3-9]\d{9}$/.test(this.memberSearchValue);
                },

                // 验证新会员数据
                isValidNewMember() {
                    return this.newMemberData.name.trim() &&
                           this.newMemberData.phone.trim() &&
                           this.newMemberData.birthday.trim() &&
                           /^1[3-9]\d{9}$/.test(this.newMemberData.phone);
                },

                // 验证积分操作
                isValidPointsOperation() {
                    if (!this.pointsAmount || this.pointsAmount <= 0) return false;

                    if (this.pointsOperation === 'deduct') {
                        return this.pointsAmount <= this.foundMember.points && this.pointsRemark.trim();
                    }

                    return true;
                }
            },

            methods: {
                // 更新当前时间
                updateCurrentTime() {
                    const now = new Date();
                    const options = {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        hour12: false
                    };
                    this.currentTime = now.toLocaleString('zh-CN', options).replace(',', '');
                },

                // 切换用户菜单
                toggleUserMenu(event) {
                    this.showUserDropdown = !this.showUserDropdown;

                    if (this.showUserDropdown) {
                        const rect = event.currentTarget.getBoundingClientRect();
                        this.userDropdownStyle = {
                            top: rect.bottom + 'px',
                            right: (window.innerWidth - rect.right) + 'px'
                        };

                        // 点击其他区域关闭下拉菜单
                        this.$nextTick(() => {
                            document.addEventListener('click', this.closeUserDropdown);
                        });
                    } else {
                        document.removeEventListener('click', this.closeUserDropdown);
                    }
                },

                // 关闭用户下拉菜单
                closeUserDropdown(event) {
                    if (!event.target.closest('.user-menu') && !event.target.closest('.user-dropdown')) {
                        this.showUserDropdown = false;
                        document.removeEventListener('click', this.closeUserDropdown);
                    }
                },

                // 处理用户菜单操作
                handleUserMenuAction(action) {
                    this.showUserDropdown = false;
                    document.removeEventListener('click', this.closeUserDropdown);

                    switch (action) {
                        case 'profile':
                            this.showNotification('个人信息功能即将上线', 'info');
                            break;
                        case 'settings':
                            this.showNotification('系统设置功能即将上线', 'info');
                            break;
                        case 'logout':
                            this.showConfirm('退出登录', '确定要退出登录吗？', () => {
                                this.showNotification('已退出登录', 'success');
                            });
                            break;
                    }
                },

                // 搜索输入处理
                onSearchInput() {
                    this.currentPage = 1; // 重置到第一页
                    this.resetInfiniteScroll(); // 重置无限滚动
                },

                // 选择分类
                selectCategory(category) {
                    this.selectedCategory = category;
                    this.currentPage = 1; // 重置到第一页
                    this.resetInfiniteScroll(); // 重置无限滚动
                },

                // 切换页码
                changePage(page) {
                    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
                        this.currentPage = page;
                    }
                },

                // 无限滚动相关方法
                // 重置无限滚动状态
                resetInfiniteScroll() {
                    this.displayedProducts = [];
                    this.currentPage = 1;
                    this.hasMoreData = true;
                    this.isLoading = false;
                    this.$nextTick(() => {
                        this.initializeProducts();
                    });
                },

                // 初始化商品列表（优化版本）
                initializeProducts() {
                    // 重置所有状态
                    this.displayedProducts = [];
                    this.isLoading = false;
                    this.hasMoreData = true;
                    this.loadError = false;
                    this.loadErrorMessage = '';
                    this.retryCount = 0;

                    // 清除防抖定时器
                    if (this.scrollDebounceTimer) {
                        clearTimeout(this.scrollDebounceTimer);
                        this.scrollDebounceTimer = null;
                    }

                    // 如果没有筛选结果，直接返回
                    if (this.filteredProducts.length === 0) {
                        this.hasMoreData = false;
                        return;
                    }

                    // 加载初始商品
                    const initialLoad = this.filteredProducts.slice(0, this.itemsPerLoad);
                    this.displayedProducts = [...initialLoad];
                    this.hasMoreData = this.filteredProducts.length > this.itemsPerLoad;

                    // 滚动到顶部
                    this.$nextTick(() => {
                        const container = document.getElementById('products-container');
                        if (container) {
                            container.scrollTop = 0;
                        }
                    });
                },

                // 加载更多商品（优化版本）
                loadMoreProducts() {
                    if (this.isLoading || !this.hasMoreData || this.loadError) {
                        return;
                    }

                    this.isLoading = true;
                    this.loadError = false;

                    // 模拟网络请求，包含错误处理
                    setTimeout(() => {
                        try {
                            // 模拟网络错误（10%概率）
                            if (Math.random() < 0.1 && this.retryCount < this.maxRetries) {
                                throw new Error('网络连接失败');
                            }

                            const currentLength = this.displayedProducts.length;
                            const nextBatch = this.filteredProducts.slice(
                                currentLength,
                                currentLength + this.itemsPerLoad
                            );

                            if (nextBatch.length > 0) {
                                // 使用Vue.set确保响应式更新
                                this.displayedProducts.push(...nextBatch);
                                this.retryCount = 0; // 重置重试次数
                            }

                            // 检查是否还有更多数据
                            this.hasMoreData = this.displayedProducts.length < this.filteredProducts.length;
                            this.isLoading = false;

                        } catch (error) {
                            this.isLoading = false;
                            this.loadError = true;
                            this.loadErrorMessage = error.message || '加载失败，请重试';
                            this.retryCount++;

                            console.error('加载商品失败:', error);
                            this.showNotification('加载商品失败，请重试', 'error');
                        }
                    }, 300 + Math.random() * 200); // 300-500ms延迟，模拟真实网络波动
                },

                // 重试加载
                retryLoad() {
                    this.loadError = false;
                    this.loadErrorMessage = '';
                    this.loadMoreProducts();
                },

                // 处理滚动事件（带防抖）
                handleScroll(event) {
                    // 清除之前的防抖定时器
                    if (this.scrollDebounceTimer) {
                        clearTimeout(this.scrollDebounceTimer);
                    }

                    // 设置防抖延迟
                    this.scrollDebounceTimer = setTimeout(() => {
                        const container = event.target;
                        const scrollTop = container.scrollTop;
                        const scrollHeight = container.scrollHeight;
                        const clientHeight = container.clientHeight;

                        // 计算距离底部的距离
                        const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

                        // 当距离底部小于阈值时，加载更多数据
                        if (distanceFromBottom < this.loadThreshold && this.hasMoreData && !this.isLoading && !this.loadError) {
                            this.loadMoreProducts();
                        }
                    }, 100); // 100ms防抖延迟
                },

                // 键盘导航支持
                handleKeyNavigation(event) {
                    const container = document.getElementById('products-container');
                    if (!container) return;

                    const scrollAmount = 100; // 每次滚动的像素数

                    switch (event.key) {
                        case 'ArrowUp':
                            event.preventDefault();
                            container.scrollTop -= scrollAmount;
                            break;
                        case 'ArrowDown':
                            event.preventDefault();
                            container.scrollTop += scrollAmount;
                            break;
                        case 'PageUp':
                            event.preventDefault();
                            container.scrollTop -= container.clientHeight * 0.8;
                            break;
                        case 'PageDown':
                            event.preventDefault();
                            container.scrollTop += container.clientHeight * 0.8;
                            break;
                        case 'Home':
                            event.preventDefault();
                            container.scrollTop = 0;
                            break;
                        case 'End':
                            event.preventDefault();
                            container.scrollTop = container.scrollHeight;
                            break;
                    }
                },

                // 获取库存状态样式
                getStockClass(stock) {
                    if (stock === 0) return 'out';
                    if (stock <= 10) return 'low';
                    return '';
                },

                // 获取库存文本
                getStockText(product) {
                    if (product.stock === 0) return '已售罄';
                    if (product.stock <= 10) return `库存紧张: ${product.stock}`;
                    return `库存: ${product.stock}`;
                },

                // 获取商品在购物车中的数量
                getProductQuantityInCart(productId) {
                    const cartItem = this.cartItems.find(item => item.id === productId);
                    return cartItem ? cartItem.quantity : 0;
                },

                // 获取商品库存
                getProductStock(productId) {
                    const product = this.productData.find(p => p.id === productId);
                    return product ? product.stock : 0;
                },

                // 显示商品详情
                showProductDetail(product) {
                    if (product.stock > 0) {
                        this.showNotification(`查看商品详情: ${product.name}`, 'info');
                    }
                },

                // 添加到购物车
                addToCart(product) {
                    if (product.stock === 0) {
                        this.showNotification('商品已售罄', 'warning');
                        return;
                    }

                    const existingItem = this.cartItems.find(item => item.id === product.id);

                    if (existingItem) {
                        existingItem.quantity += 1;
                    } else {
                        this.cartItems.push({
                            id: product.id,
                            name: product.name,
                            price: product.price,
                            quantity: 1,
                            image: product.image
                        });
                    }

                    this.showNotification(`已添加: ${product.name}`, 'success');

                    // 智能滚动：确保新添加的商品可见，优先保持第一个商品可见
                    this.$nextTick(() => {
                        const cartContainer = document.getElementById('cart-items-container');
                        if (cartContainer) {
                            const containerHeight = cartContainer.clientHeight;
                            const scrollHeight = cartContainer.scrollHeight;
                            const items = cartContainer.querySelectorAll('.order-item');

                            // 如果只有1-2个商品，保持在顶部
                            if (items.length <= 2) {
                                cartContainer.scrollTop = 0;
                                return;
                            }

                            // 如果内容高度超过容器高度，才需要滚动
                            if (scrollHeight > containerHeight && items.length > 2) {
                                // 计算最后一个商品的位置
                                const lastItem = cartContainer.lastElementChild;
                                if (lastItem && lastItem.classList.contains('order-item')) {
                                    const lastItemRect = lastItem.getBoundingClientRect();
                                    const containerRect = cartContainer.getBoundingClientRect();

                                    // 如果最后一个商品不在可视区域内，才滚动
                                    if (lastItemRect.bottom > containerRect.bottom) {
                                        // 滚动到合适位置：让最后一个商品底部对齐容器底部
                                        const scrollTo = cartContainer.scrollTop + (lastItemRect.bottom - containerRect.bottom) + 10;
                                        cartContainer.scrollTop = Math.min(scrollTo, cartContainer.scrollHeight - containerHeight);
                                    }
                                }
                            }
                        }
                    });
                },

                // 更新购物车商品数量
                updateCartItemQuantity(itemId, change) {
                    const item = this.cartItems.find(item => item.id === itemId);
                    if (!item) return;

                    const newQuantity = item.quantity + change;
                    const productStock = this.getProductStock(itemId);

                    // 检查边界条件
                    if (newQuantity <= 0) {
                        this.removeCartItem(itemId);
                        this.showNotification(`已移除：${item.name}`, 'info');
                        return;
                    }

                    if (newQuantity > productStock) {
                        this.showNotification(`库存不足，${item.name}最多只能添加${productStock}个`, 'warning');
                        return;
                    }

                    // 更新数量
                    item.quantity = newQuantity;
                    this.showNotification(`${item.name} 数量已更新为 ${newQuantity}`, 'success');
                },

                // 设置购物车商品数量
                setCartItemQuantity(itemId, value) {
                    const item = this.cartItems.find(item => item.id === itemId);
                    if (!item) return;

                    const quantity = parseInt(value);
                    const productStock = this.getProductStock(itemId);

                    if (isNaN(quantity) || quantity < 1) {
                        return;
                    }

                    if (quantity > productStock) {
                        this.showNotification(`库存不足，${item.name}最多只能添加${productStock}个`, 'warning');
                        return;
                    }

                    item.quantity = quantity;
                },

                // 处理数量输入
                handleQuantityInput(itemId, value) {
                    // 实时处理输入，但不立即应用
                    const quantity = parseInt(value);
                    if (!isNaN(quantity) && quantity >= 1) {
                        this.setCartItemQuantity(itemId, value);
                    }
                },

                // 验证数量输入
                validateQuantityInput(event, itemId) {
                    const item = this.cartItems.find(item => item.id === itemId);
                    if (!item) return;

                    const value = parseInt(event.target.value);
                    const productStock = this.getProductStock(itemId);

                    if (isNaN(value) || value < 1) {
                        event.target.value = 1;
                        this.setCartItemQuantity(itemId, 1);
                        this.showNotification(`${item.name} 数量已重置为 1`, 'info');
                    } else if (value > productStock) {
                        event.target.value = productStock;
                        this.setCartItemQuantity(itemId, productStock);
                        this.showNotification(`${item.name} 数量已调整为库存上限 ${productStock}`, 'warning');
                    } else {
                        this.setCartItemQuantity(itemId, value);
                    }
                },

                // 移除购物车商品
                removeCartItem(itemId) {
                    const index = this.cartItems.findIndex(item => item.id === itemId);
                    if (index > -1) {
                        this.cartItems.splice(index, 1);
                    }
                },

                // 清空购物车
                clearCart() {
                    if (this.cartItems.length === 0) {
                        this.showNotification('购物车已经是空的', 'info');
                        return;
                    }

                    this.showConfirm('清空购物车', '确定要清空当前购物车吗？', () => {
                        this.cartItems = [];
                        this.showNotification('购物车已清空', 'success');
                    });
                },



                // 确保滚动条可见
                ensureScrollbarVisible() {
                    const rightPanel = document.querySelector('.right-panel');
                    if (rightPanel) {
                        // 强制设置样式
                        rightPanel.style.overflowY = 'scroll';
                        rightPanel.style.height = '600px';
                        rightPanel.style.maxHeight = '600px';

                        console.log('右侧面板高度:', rightPanel.scrollHeight);
                        console.log('右侧面板可视高度:', rightPanel.clientHeight);
                        console.log('是否需要滚动:', rightPanel.scrollHeight > rightPanel.clientHeight);
                    }
                },

                // 切换订单备注展开状态
                toggleNotesExpanded() {
                    this.notesExpanded = !this.notesExpanded;
                },

                // 更新备注字数统计
                updateNotesCount() {
                    // 字数统计在模板中通过计算属性实现
                },

                // 添加快速备注
                addQuickNote(note) {
                    if (this.orderNotes) {
                        this.orderNotes += '，' + note;
                    } else {
                        this.orderNotes = note;
                    }

                    // 限制字数
                    if (this.orderNotes.length > 200) {
                        this.orderNotes = this.orderNotes.substring(0, 200);
                    }
                },

                // 切换优惠活动展开状态
                toggleDiscountExpanded() {
                    this.discountExpanded = !this.discountExpanded;
                },

                // 全选/全不选优惠活动
                toggleAllDiscounts() {
                    const hasActive = this.discountData.some(d => d.active);
                    this.discountData.forEach(d => {
                        d.active = !hasActive;
                    });
                    this.updateCartTotal();
                },

                // 更新购物车总计
                updateCartTotal() {
                    // 触发计算属性重新计算
                    this.$forceUpdate();
                },

                // 处理支付
                processPayment(method, event) {
                    if (this.cartItems.length === 0) {
                        this.showNotification('购物车为空，请先添加商品', 'warning');
                        return;
                    }

                    const isComboPayment = event && (event.ctrlKey || event.metaKey);

                    if (isComboPayment) {
                        this.showNotification('组合支付功能即将上线', 'info');
                    } else {
                        this.showNotification(`使用${method}支付 ¥${this.cartTotal.toFixed(2)}`, 'success');
                        // 这里可以添加实际的支付逻辑
                    }
                },

                // 显示会员绑定模态框
                showMemberBindModal() {
                    this.showMemberModal = true;
                    this.resetMemberModal();
                },

                // 重置会员模态框
                resetMemberModal() {
                    this.memberSearchMethod = 'phone';
                    this.memberSearchValue = '';
                    this.foundMember = null;
                    this.memberSearchLoading = false;
                    this.showMemberRegistration = false;
                    this.newMemberData = {
                        name: '',
                        phone: '',
                        birthday: '',
                        level: '普通会员'
                    };
                },

                // 关闭会员模态框
                closeMemberModal() {
                    this.showMemberModal = false;
                    this.resetMemberModal();
                },

                // 切换会员搜索方法
                switchMemberSearchMethod(method) {
                    this.memberSearchMethod = method;
                    this.memberSearchValue = '';
                    this.foundMember = null;
                    this.showMemberRegistration = false;
                    // 强制更新DOM
                    this.$nextTick(() => {
                        this.$forceUpdate();
                    });
                },

                // 搜索会员
                searchMember() {
                    if (!this.memberSearchValue.trim()) {
                        this.showNotification('请输入搜索条件', 'warning');
                        return;
                    }

                    if (this.memberSearchMethod === 'phone' && !this.isValidPhone) {
                        this.showNotification('请输入正确的手机号码', 'warning');
                        return;
                    }

                    this.memberSearchLoading = true;
                    this.foundMember = null;
                    this.showMemberRegistration = false;

                    // 模拟网络请求延迟
                    setTimeout(() => {
                        const member = this.memberDatabase.find(m => {
                            if (this.memberSearchMethod === 'phone') {
                                return m.phone === this.memberSearchValue;
                            } else if (this.memberSearchMethod === 'card') {
                                return m.cardNumber === this.memberSearchValue;
                            }
                            return false;
                        });

                        this.memberSearchLoading = false;

                        if (member) {
                            this.foundMember = member;
                            this.showNotification('找到会员信息', 'success');
                        } else {
                            this.showNotification('未找到会员信息', 'warning');
                        }
                    }, 1000);
                },

                // 启动二维码扫描
                startQRScan() {
                    this.showNotification('二维码扫描功能即将上线', 'info');
                    // 模拟扫描成功
                    setTimeout(() => {
                        const randomMember = this.memberDatabase[Math.floor(Math.random() * this.memberDatabase.length)];
                        this.foundMember = randomMember;
                        this.memberSearchValue = randomMember.phone;
                        this.showNotification('扫描成功，找到会员信息', 'success');
                    }, 2000);
                },

                // 绑定会员
                bindMember() {
                    if (!this.foundMember) return;

                    this.currentMember = { ...this.foundMember };
                    this.showNotification(`已绑定会员：${this.foundMember.name}`, 'success');
                    this.closeMemberModal();

                    // 如果有会员折扣，自动应用
                    if (this.currentMember.discount < 1) {
                        this.discountData.forEach(d => {
                            if (d.type === 'memberDiscount') {
                                d.active = true;
                            }
                        });
                    }
                },

                // 注册新会员
                registerNewMember() {
                    if (!this.isValidNewMember) {
                        this.showNotification('请填写完整的会员信息', 'warning');
                        return;
                    }

                    // 检查手机号是否已存在
                    const existingMember = this.memberDatabase.find(m => m.phone === this.newMemberData.phone);
                    if (existingMember) {
                        this.showNotification('该手机号已注册会员', 'warning');
                        return;
                    }

                    // 生成新会员
                    const newMember = {
                        id: this.memberDatabase.length + 1,
                        name: this.newMemberData.name,
                        phone: this.newMemberData.phone,
                        cardNumber: `VIP${String(this.memberDatabase.length + 1).padStart(3, '0')}`,
                        level: this.newMemberData.level,
                        points: 0,
                        discount: this.getMemberDiscount(this.newMemberData.level),
                        avatar: null,
                        birthday: this.newMemberData.birthday,
                        registerDate: new Date().toISOString().split('T')[0]
                    };

                    // 添加到数据库
                    this.memberDatabase.push(newMember);

                    // 绑定新会员
                    this.currentMember = newMember;
                    this.showNotification(`注册成功！已绑定会员：${newMember.name}`, 'success');
                    this.closeMemberModal();
                },

                // 获取会员等级对应的折扣
                getMemberDiscount(level) {
                    switch (level) {
                        case '钻石会员': return 0.85;
                        case '金卡会员': return 0.9;
                        case '银卡会员': return 0.95;
                        default: return 1.0;
                    }
                },

                // 继续购物（不绑定会员）
                continueWithoutMember() {
                    this.closeMemberModal();
                    this.showNotification('已跳过会员绑定', 'info');
                },

                // 积分管理相关方法
                closePointsModal() {
                    this.showPointsManagement = false;
                    this.pointsOperation = 'add';
                    this.pointsAmount = '';
                    this.pointsRemark = '';
                },

                executePointsOperation() {
                    if (!this.isValidPointsOperation) {
                        this.showNotification('请填写正确的操作信息', 'warning');
                        return;
                    }

                    const amount = parseInt(this.pointsAmount);
                    const member = this.foundMember;

                    if (this.pointsOperation === 'add') {
                        member.points += amount;
                        this.showNotification(`成功增加 ${amount} 积分`, 'success');
                    } else if (this.pointsOperation === 'deduct') {
                        member.points -= amount;
                        this.showNotification(`成功扣除 ${amount} 积分`, 'success');
                    }

                    // 更新当前绑定的会员信息
                    if (this.currentMember && this.currentMember.id === member.id) {
                        this.currentMember.points = member.points;
                    }

                    this.closePointsModal();
                },

                selectExchangeItem(item) {
                    if (this.foundMember.points < item.points) {
                        this.showNotification('积分不足，无法兑换', 'warning');
                        return;
                    }

                    if (confirm(`确认使用 ${item.points} 积分兑换 ${item.name}？`)) {
                        this.foundMember.points -= item.points;

                        // 更新当前绑定的会员信息
                        if (this.currentMember && this.currentMember.id === this.foundMember.id) {
                            this.currentMember.points = this.foundMember.points;
                        }

                        this.showNotification(`成功兑换 ${item.name}`, 'success');
                        this.closePointsModal();
                    }
                },

                viewMemberHistory() {
                    this.showNotification('会员消费记录功能即将上线', 'info');
                },

                // 显示条码扫描器
                showBarcodeScanner() {
                    this.showNotification('条码扫描功能即将上线', 'info');
                },

                // 显示通知
                showNotification(message, type = 'info') {
                    const notification = {
                        id: Date.now(),
                        message,
                        type
                    };

                    this.notifications.push(notification);

                    // 2秒后自动移除（缩短显示时间）
                    setTimeout(() => {
                        this.removeNotification(notification.id);
                    }, 2000);
                },

                // 移除通知
                removeNotification(id) {
                    const index = this.notifications.findIndex(n => n.id === id);
                    if (index > -1) {
                        this.notifications.splice(index, 1);
                    }
                },

                // 获取通知图标
                getNotificationIcon(type) {
                    switch (type) {
                        case 'success': return 'fa-check-circle';
                        case 'error': return 'fa-times-circle';
                        case 'warning': return 'fa-exclamation-triangle';
                        default: return 'fa-info-circle';
                    }
                },

                // 显示确认对话框
                showConfirm(title, message, confirmCallback) {
                    // 简化版确认对话框
                    if (confirm(`${title}\n\n${message}`)) {
                        if (confirmCallback) confirmCallback();
                    }
                },

                // 显示加载状态
                showLoading(message = '加载中...') {
                    this.loading.show = true;
                    this.loading.message = message;

                    // 返回隐藏函数
                    return () => {
                        this.loading.show = false;
                    };
                },

                // 处理全局键盘事件
                handleGlobalKeydown(event) {
                    switch (event.key) {
                        case 'F1':
                            event.preventDefault();
                            this.showNotification('正在打开帮助文档...', 'info');
                            break;
                        case 'F2':
                            event.preventDefault();
                            this.showNotification('已启动快速收银模式', 'success');
                            break;
                        case 'Escape':
                            event.preventDefault();
                            this.showConfirm('取消操作', '确定要取消当前操作吗？', () => {
                                this.showNotification('已取消当前操作', 'warning');
                            });
                            break;
                    }
                },

                // 调整布局
                adjustLayout() {
                    // 动态调整布局的逻辑
                    this.$nextTick(() => {
                        const productsContainer = document.getElementById('products-container');
                        const cartContainer = document.getElementById('cart-items-container');

                        if (!productsContainer || !cartContainer) return;

                        // 根据窗口高度调整容器高度
                        const windowHeight = window.innerHeight;
                        const headerHeight = document.querySelector('.header')?.offsetHeight || 60;
                        const footerHeight = document.querySelector('.footer')?.offsetHeight || 40;
                        const searchSectionHeight = document.querySelector('.search-section')?.offsetHeight || 70;
                        const paginationHeight = document.querySelector('.pagination-container')?.offsetHeight || 52;

                        const availableHeight = windowHeight - headerHeight - footerHeight;
                        productsContainer.style.height = `${availableHeight - searchSectionHeight - paginationHeight}px`;

                        // 注释掉购物车容器高度限制，让内容自然展开
                        // const memberSectionHeight = document.querySelector('.member-section')?.offsetHeight || 60;
                        // const orderHeaderHeight = document.querySelector('.order-header')?.offsetHeight || 50;
                        // const discountSectionHeight = document.querySelector('.discount-section')?.offsetHeight || 80;
                        // const paymentSectionHeight = document.querySelector('.payment-section')?.offsetHeight || 200;

                        // const cartMaxHeight = availableHeight - memberSectionHeight - orderHeaderHeight - discountSectionHeight - paymentSectionHeight;
                        // cartContainer.style.maxHeight = `${cartMaxHeight}px`;
                    });
                }
            },

            mounted() {
                // 初始化时间并每秒更新
                this.updateCurrentTime();
                setInterval(this.updateCurrentTime, 1000);

                // 添加全局键盘事件
                document.addEventListener('keydown', this.handleGlobalKeydown);

                // 添加商品区域键盘导航支持
                document.addEventListener('keydown', this.handleKeyNavigation);

                // 添加窗口大小变化监听
                window.addEventListener('resize', this.adjustLayout);

                // 初始调整布局
                this.$nextTick(() => {
                    this.adjustLayout();
                    // 初始化无限滚动商品列表
                    this.initializeProducts();
                    // 确保滚动条可见
                    this.ensureScrollbarVisible();
                });
            },

            beforeDestroy() {
                // 清理事件监听器
                document.removeEventListener('keydown', this.handleGlobalKeydown);
                document.removeEventListener('keydown', this.handleKeyNavigation);
                document.removeEventListener('click', this.closeUserDropdown);
                window.removeEventListener('resize', this.adjustLayout);

                // 清理防抖定时器
                if (this.scrollDebounceTimer) {
                    clearTimeout(this.scrollDebounceTimer);
                }
            }
        });

        // 将Vue实例绑定到window对象，以便onclick事件可以访问
        window.vueApp = document.querySelector('#app').__vue__;

        // CSS样式已合并到cashier-merged.css文件中












    </script>
</body>
</html>