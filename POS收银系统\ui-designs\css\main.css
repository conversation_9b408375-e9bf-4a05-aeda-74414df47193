/* 全局样式设置 - 现代化优化版 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Inter', 'SF Pro Display', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

:root {
    /* 现代化精致配色方案 */
    --primary-color: #2563eb;
    --primary-light: #dbeafe;
    --primary-dark: #1d4ed8;
    --primary-gradient: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);

    --secondary-color: #f59e0b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;

    /* 精细化文字颜色 */
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-tertiary: #64748b;
    --text-light: #94a3b8;
    --text-muted: #cbd5e1;

    /* 背景色系 */
    --bg-color: #ffffff;
    --bg-light: #f8fafc;
    --bg-lighter: #f1f5f9;
    --bg-dark: #e2e8f0;

    /* 边框和分割线 */
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    --border-dark: #cbd5e1;

    /* 阴影系统 */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* 圆角系统 */
    --border-radius-xs: 2px;
    --border-radius-sm: 4px;
    --border-radius-md: 6px;
    --border-radius-lg: 8px;
    --border-radius-xl: 12px;
    --border-radius-2xl: 16px;

    /* 精细化间距系统 */
    --spacing-0: 0;
    --spacing-1: 2px;
    --spacing-2: 4px;
    --spacing-3: 6px;
    --spacing-4: 8px;
    --spacing-5: 10px;
    --spacing-6: 12px;
    --spacing-8: 16px;
    --spacing-10: 20px;
    --spacing-12: 24px;
    --spacing-16: 32px;
    --spacing-20: 40px;

    /* 字体大小系统 */
    --text-xs: 11px;
    --text-sm: 12px;
    --text-base: 13px;
    --text-lg: 14px;
    --text-xl: 16px;
    --text-2xl: 18px;
    --text-3xl: 20px;

    /* 字重系统 */
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
}

body {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: var(--text-primary);
    font-size: var(--text-base);
    font-weight: var(--font-normal);
    line-height: 1.4;
    letter-spacing: -0.01em;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    overflow-x: hidden;
}

#app {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    width: 100%;
    overflow-x: hidden;
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: color 0.2s ease;
}

a:hover {
    color: var(--primary-dark);
}

button {
    cursor: pointer;
    border: none;
    outline: none;
    background: none;
    transition: all 0.2s ease;
}

input, select, textarea {
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-4) var(--spacing-6);
    outline: none;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    color: var(--text-primary);
    letter-spacing: -0.01em;
}

input:focus, select:focus, textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    background: #ffffff;
    transform: translateY(-1px);
}

input::placeholder {
    color: var(--text-light);
    font-weight: var(--font-normal);
}

/* 布局组件 - 现代化优化 */
.header {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    height: 56px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 var(--spacing-12);
    box-shadow: var(--shadow-sm);
    border-bottom: 1px solid var(--border-light);
    position: relative;
    z-index: 10;
    backdrop-filter: blur(8px);
}

.header-left, .header-right {
    display: flex;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    color: var(--primary-color);
    margin-right: var(--spacing-12);
    letter-spacing: -0.02em;
}

.logo i {
    font-size: var(--text-2xl);
    margin-right: var(--spacing-4);
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.store-info {
    display: flex;
    flex-direction: column;
    font-size: var(--text-sm);
    gap: var(--spacing-1);
}

.store-name {
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    font-size: var(--text-base);
}

.operator {
    font-size: var(--text-xs);
    color: var(--text-tertiary);
    font-weight: var(--font-medium);
}

.time-display {
    display: flex;
    align-items: center;
    margin-right: var(--spacing-12);
    color: var(--text-secondary);
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    padding: var(--spacing-3) var(--spacing-6);
    border-radius: var(--border-radius-lg);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-xs);
}

.time-display i {
    margin-right: var(--spacing-3);
    color: var(--primary-color);
    font-size: var(--text-sm);
}

.user-menu {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 6px 12px;
    border-radius: var(--border-radius-md);
    transition: all 0.2s ease;
    background-color: var(--bg-light);
}

.user-menu:hover {
    background-color: var(--primary-light);
}

.user-menu i:first-child {
    margin-right: var(--spacing-sm);
    color: var(--primary-color);
}

.user-menu .fa-chevron-down {
    margin-left: var(--spacing-sm);
    font-size: 12px;
    color: var(--text-secondary);
}

.main-content {
    display: flex;
    flex: 1;
    overflow: visible;
    background-color: var(--bg-light);
    min-height: 0;
}

.footer {
    background-color: var(--bg-color);
    height: 48px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    color: var(--text-light);
    font-size: 12px;
}

.status-info {
    display: flex;
}

.status-item {
    display: flex;
    align-items: center;
    margin-right: var(--spacing-lg);
    padding: 4px 10px;
    background-color: var(--bg-light);
    border-radius: var(--border-radius-sm);
}

.status-item i {
    margin-right: var(--spacing-sm);
    color: var(--success-color);
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 16px;
    border-radius: var(--border-radius-md);
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background-color: #E05F00;
    transform: translateY(-1px);
}

.btn-outline {
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    background-color: transparent;
}

.btn-outline:hover {
    background-color: var(--primary-light);
    transform: translateY(-1px);
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: var(--border-radius-sm);
}

.btn-lg {
    padding: 12px 20px;
    font-size: 16px;
    border-radius: var(--border-radius-lg);
}

.btn i {
    margin-right: var(--spacing-sm);
}

/* 卡片组件 */
.card {
    background-color: var(--bg-color);
    border-radius: var(--border-radius-lg);
    box-shadow: 0 2px 8px var(--shadow-color);
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-body {
    padding: var(--spacing-md);
}

.card-footer {
    padding: var(--spacing-md);
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-light);
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
}

.table th, .table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background-color: var(--bg-light);
    font-weight: 600;
    color: var(--text-secondary);
}

.table tr:hover {
    background-color: var(--primary-light);
}

/* 表单样式 */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-secondary);
}

.form-control {
    width: 100%;
}

/* 工具类 */
.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--secondary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }

.bg-primary { background-color: var(--primary-color); }
.bg-secondary { background-color: var(--secondary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-danger { background-color: var(--danger-color); }

.text-center { text-align: center; }
.text-right { text-align: right; }
.text-bold { font-weight: 600; }

.mt-10 { margin-top: 10px; }
.mb-10 { margin-bottom: 10px; }
.ml-10 { margin-left: 10px; }
.mr-10 { margin-right: 10px; }

.p-10 { padding: 10px; }
.p-15 { padding: 15px; }
.p-20 { padding: 20px; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.justify-between { justify-content: space-between; }
.items-center { align-items: center; }
.flex-1 { flex: 1; }

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: var(--text-light);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
} 