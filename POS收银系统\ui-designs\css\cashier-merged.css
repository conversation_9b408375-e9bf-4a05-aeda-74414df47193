/*
 * POS收银系统 - 完整合并后的样式文件
 * 合并了 cashier.css 和 main-cashier.css，去除重复定义
 * 包含购物车宽度修复和其他优化
 * 包含所有必要的基础样式和布局
 */

/* ========== 基础页面布局样式 ========== */
body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f5f5f5;
    height: 100vh;
    overflow: hidden;
}

#app {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100vw;
    overflow: hidden;
}

/* ========== 头部导航栏样式 ========== */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1000;
    flex-shrink: 0;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 30px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 18px;
    font-weight: 600;
}

.logo i {
    font-size: 24px;
    color: #ffd700;
}

.store-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.store-name {
    font-size: 14px;
    font-weight: 500;
}

.operator {
    font-size: 12px;
    opacity: 0.8;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.time-display {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 20px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.user-menu:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

/* ========== 主要内容区域 ========== */
.main-content {
    display: flex;
    flex: 1;
    height: calc(100vh - 100px); /* 减去头部和底部高度 */
    overflow: hidden;
    background-color: #f8fafc;
}

/* ========== 左侧商品区域 ========== */
.left-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: var(--bg-color, #ffffff);
    border-right: 1px solid var(--border-color, #e2e8f0);
    overflow: hidden;
    min-width: 0;
    max-width: calc(100% - 420px); /* 修复：适配新的右侧面板宽度 */
    height: 100%;
    min-height: 100%;
    position: relative;
    isolation: isolate;
}

/* ========== 搜索和分类区域 ========== */
.search-section {
    padding: 16px;
    border-bottom: 1px solid var(--border-color, #e2e8f0);
    background-color: var(--bg-color, #ffffff);
    position: relative;
    flex-shrink: 0;
}

.search-box {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 12px;
    padding: 0 16px;
    margin-bottom: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    height: 40px;
}

.search-box:focus-within {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-color: #3b82f6;
    transform: translateY(-1px);
}

.search-box i {
    color: #94a3b8;
    margin-right: 12px;
    font-size: 16px;
}

.search-box input {
    flex: 1;
    border: none;
    background: transparent;
    height: 100%;
    font-size: 14px;
    font-weight: 500;
    color: #0f172a;
    padding: 0;
    letter-spacing: -0.01em;
}

.search-box input:focus {
    box-shadow: none;
    outline: none;
}

.search-box input::placeholder {
    color: #94a3b8;
    font-weight: 400;
}

.scan-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 12px;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    margin-left: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: none;
    cursor: pointer;
}

.scan-btn:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.scan-btn:active {
    transform: translateY(0) scale(0.98);
}

.scan-btn i {
    font-size: 16px;
    color: white;
    margin: 0;
}

.category-tabs {
    display: flex;
    overflow-x: auto;
    white-space: nowrap;
    padding-bottom: 8px;
    margin: 0 -8px;
    scrollbar-width: thin;
    gap: 8px;
}

.category-tabs::-webkit-scrollbar {
    height: 3px;
}

.category-tabs::-webkit-scrollbar-thumb {
    background-color: #e2e8f0;
    border-radius: 4px;
}

.category-tab {
    padding: 8px 20px;
    margin: 0 4px;
    border-radius: 20px;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    color: #475569;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    white-space: nowrap;
    border: 1px solid #f1f5f9;
    letter-spacing: -0.01em;
    min-height: 32px;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.category-tab:hover {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #3b82f6;
    border-color: #3b82f6;
    transform: translateY(-1px);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.category-tab.active {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    border-color: #3b82f6;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

/* ========== 商品网格 ========== */
.products-grid {
    flex: 1;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
    padding: 20px 20px 0 20px;
    overflow-y: scroll;
    overflow-x: hidden;
    align-content: start;
    height: calc(100vh - 170px);
    max-height: calc(100vh - 170px);
    min-height: 400px;
    position: relative;
    grid-auto-rows: minmax(min-content, max-content);
    overscroll-behavior: contain;
    z-index: 1;
    scrollbar-gutter: stable;
    scroll-behavior: smooth;
}

/* ========== 通知系统样式 (来自main-cashier.css) ========== */
.notifications-container {
    position: fixed;
    top: 80px;
    left: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 350px;
}

.notification {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 12px 16px;
    display: flex;
    align-items: center;
    min-width: 300px;
    max-width: 400px;
    transform: translateX(120%);
    transition: transform 0.3s ease;
    animation: slideIn 0.3s forwards;
}

.notification-icon {
    margin-right: 12px;
    font-size: 20px;
}

.notification-content {
    flex: 1;
    font-size: 14px;
}

.notification-close {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 14px;
    color: #999;
    padding: 4px;
}

.notification.success .notification-icon {
    color: #52c41a;
}

.notification.error .notification-icon {
    color: #ff4d4f;
}

.notification.warning .notification-icon {
    color: #faad14;
}

.notification.info .notification-icon {
    color: #1890ff;
}

@keyframes slideIn {
    to {
        transform: translateX(0);
    }
}

/* ========== 商品按钮数量徽章样式 (来自main-cashier.css) ========== */
.add-btn {
    position: relative;
}

.add-btn.has-quantity {
    background: linear-gradient(135deg, #52c41a, #389e0d);
    color: white;
    border-color: #52c41a;
}

.add-btn.has-quantity:hover {
    background: linear-gradient(135deg, #389e0d, #237804);
    border-color: #389e0d;
}

.quantity-badge {
    position: absolute;
    top: -6px;
    right: -6px;
    background: #ff4d4f;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    z-index: 1;
}

/* ========== 商品卡片样式 ========== */
.product-card {
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.08);
    padding: 16px;
    display: flex;
    flex-direction: column;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    border: 1px solid rgba(226, 232, 240, 0.8);
    overflow: hidden;
    height: 100%;
    min-height: 200px;
    justify-content: space-between;
    backdrop-filter: blur(10px);
}

.product-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08), 0 3px 10px rgba(0, 0, 0, 0.12);
    border-color: rgba(59, 130, 246, 0.3);
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.product-card.out-of-stock {
    opacity: 0.6;
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
    border-color: rgba(156, 163, 175, 0.3);
}

.product-card.out-of-stock:hover {
    transform: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.08);
    border-color: rgba(156, 163, 175, 0.3);
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
}

.product-image {
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 14px;
    overflow: hidden;
    border-radius: 12px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
    width: 100%;
    flex-shrink: 0;
    border: 1px solid rgba(226, 232, 240, 0.6);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.product-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 0;
}

.product-info h4 {
    font-size: 16px;
    margin-bottom: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.4;
    letter-spacing: -0.01em;
}

.price {
    color: #dc2626;
    font-weight: 700;
    font-size: 19px;
    margin-bottom: 8px;
    display: flex;
    align-items: baseline;
    letter-spacing: -0.02em;
}

.price .original-price {
    font-size: 12px;
    color: #9ca3af;
    text-decoration: line-through;
    margin-left: 8px;
    font-weight: 500;
    opacity: 0.8;
}

.stock {
    font-size: 12px;
    color: #6b7280;
    display: flex;
    align-items: center;
    margin-top: auto;
    font-weight: 500;
    padding: 3px 8px;
    border-radius: 8px;
    background: rgba(243, 244, 246, 0.8);
    width: fit-content;
}

.stock::before {
    content: "";
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #10b981;
    margin-right: 4px;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.stock.low {
    background: rgba(251, 191, 36, 0.1);
    color: #d97706;
}

.stock.low::before {
    background-color: #f59e0b;
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
}

.stock.out {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.stock.out::before {
    background-color: #ef4444;
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.product-card .add-btn {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3), 0 1px 3px rgba(59, 130, 246, 0.2);
    z-index: 3;
    border: none;
    font-size: 14px;
    cursor: pointer;
}

.product-card .add-btn:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4), 0 2px 6px rgba(59, 130, 246, 0.3);
}

.product-card .add-btn:disabled {
    background-color: #94a3b8;
    cursor: not-allowed;
    box-shadow: none;
    opacity: 0.5;
}

.product-card .add-btn:disabled:hover {
    transform: none;
    box-shadow: none;
}

/* ========== 右侧收银区域 - 包含宽度修复 ========== */
.right-panel {
    width: 420px; /* 修复：从340px增加到420px */
    min-width: 420px;
    display: flex;
    flex-direction: column;
    background: #ffffff;
    border-left: 1px solid #e2e8f0;
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.08);
    height: calc(100vh - 140px); /* 修复：调整高度计算，考虑头部60px+底部40px+边距 */
    max-height: calc(100vh - 140px);
    position: relative;
    overflow-y: auto; /* 修复：允许整个面板滚动 */
    overflow-x: hidden;
    flex-shrink: 0;
}

.right-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, #3366ff 50%, transparent 100%);
    z-index: 1;
}

/* ========== 会员信息区域 ========== */
.member-section {
    padding: 20px;
    margin: 16px;
    margin-bottom: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.25);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
    min-height: 80px;
    position: relative;
    overflow: hidden;
    color: white;
}

.member-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.35);
}

.member-info {
    display: flex;
    align-items: center;
    gap: 16px;
    position: relative;
    z-index: 2;
}

.member-info i {
    font-size: 24px;
    color: rgba(255, 255, 255, 0.9);
    background: rgba(255, 255, 255, 0.2);
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.bind-member-btn {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    letter-spacing: -0.01em;
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 2;
    cursor: pointer;
}

.bind-member-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.bind-member-btn i {
    margin-right: 8px;
    font-size: 12px;
}

/* ========== 订单区域 ========== */
.order-section {
    flex: 1; /* 修复：使用flex布局 */
    display: flex;
    flex-direction: column;
    min-height: 0; /* 修复：允许flex收缩 */
    overflow: hidden; /* 修复：防止内容溢出 */
    background: #ffffff;
    border-radius: 16px;
    margin: 0 16px 12px 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #f1f5f9;
    position: relative;
}

.order-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
    border-radius: 16px 16px 0 0;
}

/* ========== 当前订单标题 ========== */
.current-order-title {
    padding: 24px;
    border-bottom: 1px solid #f1f5f9;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    flex-shrink: 0;
    border-radius: 16px 16px 0 0;
    position: relative;
    z-index: 2;
    min-height: 64px;
}

.order-title {
    font-size: 20px;
    color: #1e293b;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
    letter-spacing: -0.02em;
    margin: 0;
}

.cart-icon {
    color: #3b82f6;
    font-size: 22px;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.order-count {
    background: linear-gradient(135deg, #ef4444, #f97316);
    color: white;
    border-radius: 20px;
    padding: 4px 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13px;
    font-weight: 700;
    margin-left: 8px;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
    animation: pulse 2s infinite;
    min-width: 24px;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.clear-btn {
    display: flex;
    align-items: center;
    color: #ef4444;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 10px 16px;
    border-radius: 12px;
    border: 2px solid rgba(239, 68, 68, 0.2);
    background: linear-gradient(135deg, #fef2f2, #fee2e2);
    gap: 8px;
    cursor: pointer;
}

.clear-btn:hover {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border-color: #ef4444;
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(239, 68, 68, 0.3);
}

.clear-btn i {
    font-size: 16px;
}

/* ========== 购物车列表容器 - 优化内边距和布局 ========== */
.cart-items-list {
    padding: 8px 12px;
    overflow-y: auto;
    max-height: 300px; /* 修复：减少最大高度，适应新的面板高度 */
    min-height: 200px; /* 修复：减少最小高度 */
    margin: 8px;
    background-color: #fafafa;
    position: relative;
    z-index: 1;
    border-radius: 12px;
    width: calc(100% - 16px);
    flex: 1;
}

/* 滚动条样式 */
.cart-items-list::-webkit-scrollbar {
    width: 6px;
    background: transparent;
}

.cart-items-list::-webkit-scrollbar-track {
    background: #f8fafc;
    border-radius: 3px;
    margin: 4px 0;
    box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.05);
}

.cart-items-list::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
    border-radius: 3px;
    min-height: 20px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.cart-items-list::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

/* ========== 购物车项样式 - 包含宽度修复 ========== */
.cart-item {
    background-color: #fff;
    border-radius: 12px;
    padding: 16px 18px; /* 修复：增加内边距 */
    margin-bottom: 12px;
    display: flex;
    flex-direction: column;
    width: 100%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* 修复：增强阴影 */
    position: relative;
    z-index: 2;
    transition: all 0.2s ease; /* 修复：添加过渡效果 */
    border: 1px solid transparent; /* 修复：添加透明边框 */
}

/* 购物车项悬停效果 - 修复 */
.cart-item:hover {
    transform: translateY(-2px); /* 修复：轻微上移 */
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15); /* 修复：增强阴影 */
    border-color: #e3f2fd; /* 修复：淡蓝色边框 */
}

/* 商品名称和价格行样式 - 优化布局 */
.item-name-price {
    display: flex;
    justify-content: space-between;
    align-items: flex-start; /* 修复：改为顶部对齐 */
    margin-bottom: 12px; /* 修复：增加间距 */
    width: 100%;
    gap: 12px; /* 修复：添加间距 */
}

/* 商品名称样式 - 优化显示空间 */
.item-name {
    font-size: 16px; /* 修复：增加字体大小 */
    font-weight: 600; /* 修复：增加字重 */
    white-space: normal; /* 修复：允许换行 */
    overflow: visible; /* 修复：显示完整内容 */
    text-overflow: unset; /* 修复：移除省略号 */
    flex: 1; /* 修复：使用flex占用剩余空间 */
    line-height: 1.4; /* 修复：增加行高 */
    color: #1a1a1a; /* 修复：更深的颜色 */
    margin-right: 8px; /* 修复：与价格保持间距 */
    word-break: break-word; /* 修复：长单词换行 */
}

/* 商品价格样式 - 优化价格显示 */
.item-price {
    font-size: 16px; /* 修复：增加字体大小 */
    color: #e74c3c; /* 修复：更好的红色 */
    white-space: nowrap;
    font-weight: 600; /* 修复：增加字重 */
    flex-shrink: 0; /* 修复：防止收缩 */
    min-width: fit-content; /* 修复：确保完整显示 */
}

/* 数量控制布局 - 优化控制区域 */
.item-quantity-control {
    display: flex;
    align-items: center;
    justify-content: center; /* 修复：居中显示 */
    height: 36px; /* 修复：增加高度 */
    width: 100%;
    margin-top: 4px; /* 修复：添加顶部间距 */
}

.quantity-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 数量控制按钮 - 优化按钮大小 */
.quantity-btn {
    min-width: 32px; /* 修复：增加按钮大小 */
    min-height: 32px;
    width: 32px;
    height: 32px;
    border-radius: 8px; /* 修复：改为圆角矩形 */
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    cursor: pointer;
    transition: all 0.2s ease; /* 修复：增强过渡效果 */
    font-size: 16px; /* 修复：增加图标大小 */
    font-weight: 600;
    border: none;
}

.quantity-btn.decrease {
    border: 1px solid #ddd;
    color: #666;
    background-color: #f9f9f9;
}

.quantity-btn.increase {
    background-color: #f0f7ff;
    border: 1px solid #4070f4;
    color: #4070f4;
}

.quantity-btn:hover {
    background-color: #4070f4;
    color: white;
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(64, 112, 244, 0.3);
}

.item-quantity {
    width: 48px; /* 修复：增加宽度 */
    height: 32px; /* 修复：增加高度 */
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 6px; /* 修复：增加圆角 */
    background-color: #fff;
    font-size: 15px; /* 修复：增加字体大小 */
    font-weight: 600; /* 修复：增加字重 */
    padding: 0;
    margin: 0 8px; /* 修复：增加左右间距 */
    transition: all 0.2s ease; /* 修复：添加过渡效果 */
}

/* 数量输入框焦点效果 - 修复 */
.item-quantity:focus {
    outline: none;
    border-color: #4070f4;
    box-shadow: 0 0 0 2px rgba(64, 112, 244, 0.2);
    transform: scale(1.05);
}

/* ========== 优惠活动区域 ========== */
.discount-section {
    margin: 0 16px 12px 16px;
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #f1f5f9;
    overflow: hidden;
    flex-shrink: 0;
}

.discount-header {
    padding: 16px 20px;
    border-bottom: 1px solid #f1f5f9;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
}

.discount-header h3 {
    font-size: 16px;
    color: #1e293b;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.discount-container {
    padding: 16px 20px;
}

.discount-items {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.discount-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-radius: 12px;
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    border: 1px solid #bbf7d0;
    transition: all 0.3s ease;
}

.discount-item.active {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border-color: #10b981;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.discount-item-text {
    font-size: 14px;
    font-weight: 500;
}

.discount-item i {
    font-size: 16px;
    color: #10b981;
}

.discount-item.active i {
    color: white;
}

/* ========== 支付区域 ========== */
.payment-section {
    margin: 0 16px 16px 16px;
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #f1f5f9;
    overflow: hidden;
    flex-shrink: 0;
}

.total-info {
    padding: 20px;
    border-bottom: 1px solid #f1f5f9;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
}

.breakdown-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
}

.breakdown-item:last-child {
    margin-bottom: 0;
}

.breakdown-item.discount {
    color: #10b981;
    font-weight: 600;
}

.breakdown-item.total {
    font-size: 18px;
    font-weight: 700;
    color: #1e293b;
    padding-top: 12px;
    border-top: 1px solid #f1f5f9;
    margin-top: 12px;
}

.payment-methods {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.payment-method {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 16px 24px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    border: none;
    position: relative;
    overflow: hidden;
}

.payment-method.primary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.payment-method.primary:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(59, 130, 246, 0.5);
}

.payment-method.primary:active {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.payment-method i {
    font-size: 20px;
}

/* ========== 空购物车状态 ========== */
.empty-cart {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    color: #64748b;
    min-height: 200px;
    flex: 1;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 12px;
    margin: 8px 0;
}

.empty-cart-icon {
    font-size: 56px;
    color: #cbd5e1;
    margin-bottom: 16px;
    opacity: 0.8;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
}

.empty-cart-text {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #475569;
    letter-spacing: -0.01em;
}

.empty-cart-hint {
    font-size: 14px;
    color: #64748b;
    margin: 0;
    line-height: 1.5;
}

/* ========== 响应式设计 ========== */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }

    .left-panel {
        max-width: 100%;
        height: 60%;
    }

    .right-panel {
        width: 100%;
        min-width: 100%;
        height: 40%;
        max-height: 40%;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 12px;
        padding: 16px;
    }

    .cart-item {
        padding: 12px 14px;
    }

    .quantity-btn {
        width: 28px;
        height: 28px;
    }

    .item-quantity {
        width: 40px;
        height: 28px;
    }
}
